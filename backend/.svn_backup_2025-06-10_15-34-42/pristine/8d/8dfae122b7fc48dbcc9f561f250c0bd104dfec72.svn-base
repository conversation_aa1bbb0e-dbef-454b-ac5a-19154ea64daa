<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/distribution.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Distribution\BucketOptions\Linear instead.
     * @deprecated
     */
    class Distribution_BucketOptions_Linear {}
}
class_exists(Distribution\BucketOptions\Linear::class);
@trigger_error('Google\Api\Distribution_BucketOptions_Linear is deprecated and will be removed in the next major release. Use Google\Api\Distribution\BucketOptions\Linear instead', E_USER_DEPRECATED);

