var waiting_qte = false; 
var waiting_comment = false; 
var ncmd_url = '/admin/orders/order.php';
$(document).ready(function(){
	if( typeof ncmd_url_sucharge != 'undefined' && $.trim(ncmd_url_sucharge)!='' ){
		ncmd_url = ncmd_url_sucharge;
	}

	$('.datepicker').each(function(){
		var temp = this ;
		$(temp).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					$(temp).DatePickerHide();
				}
			}
		});
	});

	$('.ncmd-invoice-input').live('click', function(){
		displayPopup(orderCreateSelectClient, '', '/admin/ajax/orders/ncmd-customers-edit.php', false, 650, 450);
	});

	$('.ncmd-delivery-input').live('click', function(){
		displayPopup(orderCreateAdresseLivraison, '', '/admin/ajax/orders/ncmd-delivery-edit.php', false, 650, 450);
	});

	$('#ncmd_select_product').live('click', function(){
		displayPopup(orderCreateSelectProduit, '', '/admin/ajax/catalog/ajax-product-select.php?orderable=false&multiselect=true&btn_text=Ajouter+au+panier&cnt_publish=0', false, 800, 400);
		$('.popup_ria_iframe').load(function(){
			var $elt = this;
			var innerDoc = $(this.contentDocument || this.contentWindow.document);
			innerDoc.on( 'click', '.selectprd', function () {
				$('div.success', innerDoc).remove();
				$('div.error', innerDoc).remove();
				$elt.contentWindow.scrollTo(0, 0);
				if ($('.select-prd:checked', innerDoc).length) {
					$('#popup-content', innerDoc).prepend(
						$('<div>').addClass('success').text('Les produits ont été ajoutés avec succès.')
					);
					$('.select-prd:checked', innerDoc).prop('checked', false);
					setTimeout(function() {
						$('div.success', innerDoc).slideUp('slow', function() {
							$('div.success').remove();
						});
					}, 5000);
				}else{
					$('#popup-content', innerDoc).prepend(
						$('<div>').addClass('error').text('Vous devez sélectionner des produits a ajouté à votre catalogue.')
					);
				}
			});
		});
	});
	
	$('#ncmd_add_products').live('click', function(){
		displayPopup(orderCreateAjoutProduit, '', '/admin/ajax/orders/ncmd-add-products.php', false, 800, 450);
	});

	$('input[name=del]').live('click', function(){
		if( confirm(orderCreateConfirmSuppressionProduits)){
			var data = new Array();
			$('input.ncmd_remove_prd:checked').each(function(){
				data.push('prd_del['+$(this).attr('id').replace('ncmd_remove_prd_','')+'][]='+$(this).val());
			});
			
			// Rechargement de la page avec suppression du produit cliqué
			$.get( ncmd_url+'?'+data.join('&'), function( html ){
				$('#ncmd_content').html( html );
				ncmd_init();
			});
		}
		return false;
	});
	
	// détachement du code promo
	$('input[name=detach]').live('click', function(){
		$.get( ncmd_url+'?detach=true', function( html ){
			$('#ncmd_content').html( html );
			ncmd_init();
		});
		return false; 
	});
	
	// ajout du code promo
	$('input[name=attach]').live('click', function(){
		$.get( ncmd_url+'?pmt-code='+$('input[name=pmt-code]').val(), function( html ){
			$('#ncmd_content').html( html );
			ncmd_init();
		});
		return false; 
	});
	
	// modification du statu de commande
	$('#btn-upd-state').live('click', function(){
		$.get( ncmd_url+'?newstate='+$('#newstate').val(), function( html ){
			$('#ncmd_content').html( html );
			ncmd_init();
		});
		return false; 
	});

	// modification de la quantité
	$('.ncmd_qte').live('keyup',function(){
		if( $(this).val() > 0 ) {
			var line = $(this).parents('tr:eq(0)').find('.valign-center input.ncmd_remove_prd').attr('id').replace('ncmd_remove_prd_', '');
			if( waiting_qte ){ clearTimeout(waiting_qte); }
			waiting_qte = setTimeout("ncmd_refresh_qte("+$(this).attr('name').replace('ncmd_qte_','')+","+$(this).val()+","+line+")", 500);
		}
	});

	// modification d'un commentaire sur un produit
	$('.ncmd_prd_comment').live('focusout',function(){
		var line = $(this).parents('tr:eq(0)').find('.valign-center input.ncmd_remove_prd').attr('id').replace('ncmd_remove_prd_', '');
		ncmd_refresh_prd_comment($(this).attr('name').replace('ncmd_prd_comment_',''),$(this).val(), line);
	});
	

	// lors d'un clic sur une ligne on selectionne l'utilisateur et on appel la fonction parent
	$('.trajax td').live('click', function(){
		displayPopup('Choisir le panier', '', '/admin/ajax/orders/ncmd-customers-cart.php?usr='+$(this).parents('tr').attr('data-id'), false, 575, 450);
	});

		
	ncmd_init();

});


function ncmd_init(){

}
/** Permet le rechargement du panier pour changer la quantité de celui ci */
function ncmd_refresh_qte( id, qte, line ){
	if( qte > 0 ){
		$.get( ncmd_url+'?prd_upd='+id+'&prd_qte='+qte+'&prd_line='+line, function( html ){
			$('#ncmd_content').html( html );
			
			// remet le focus dans le champs
			var $elt = $("#ncmd_remove_prd_"+line).parents('tr:eq(0)').find('.ncmd_qte[name=ncmd_qte_'+id+']');
			$elt.focus();
			var tmp = $elt.val(); 
			$elt.val( '' ).val( tmp );

			ncmd_init();
		});
	}
}
/** Permet l'enregistrement des commentaires sur les produits */
var current_ajax = new Array();
function ncmd_refresh_prd_comment( id, value, line ){
	if( typeof(current_ajax['comment_'+id]) != 'undefined' && current_ajax['comment_'+id] ) current_ajax['comment_'+id].abort();
	
	current_ajax['comment_'+id] = $.get( ncmd_url+'?prd_upd='+id+'&prd_comment='+encodeURIComponent(value)+'&prd_line='+line, function(){
		current_ajax['comment_'+id] = false;
	});
}

/** alias de ncmd_select_user() */
function parent_select_user(id){
	return ncmd_select_user(id);
}

/** alias de ncmd_select_prd() */
function parent_select_prd(id){
	return ncmd_select_prd(id);
}

/** alias de ncmd_select_prds() */
function parent_select_prds(data){
	return ncmd_select_prds(data);
}

/** alias de ncmd_refresh() */
function parent_refresh(){
	return ncmd_refresh();
}

/**	Cette fonction permet le rechargement de la page avec l'utilisateur donné
*	id : identifiant de l'utilisateur
*/
function ncmd_select_user( id ){
	if( !(id > 0) ) return;

	//rechargement de la page de création avec l'utilisateur choisi.
	$.get( ncmd_url+'?usr='+id, function( html ){
		$('#ncmd_content').html( html );
		ncmd_init();
	});
}

/** Cette fonction permet le rechargement de la page avec l'utilisateur donné et le panier
*	usr : identifiant de l'utilisateur
*	ord : identifiant de la commande
*/
function parent_select_user_ord( usr, ord ){
	if( !(usr > 0) ) return;
	if( !(ord >= 0) ) return;

	//rechargement de la page de création avec l'utilisateur choisi.
	$.get( ncmd_url+'?ord='+ord+'&usr='+usr, function( html ){
		$('#ncmd_content').html( html );
		ncmd_init();
	});
}

/**	Cette fonction permet le rechargement de la page en ajoutant un produit au panier
*	id : identifiant du produit
*/
function ncmd_select_prd( id ){
	if( !(id > 0) ) return;
	
	//rechargement de la page de création avec l'utilisateur choisi.
	$.get( ncmd_url+'?prd_add='+id, function( res ){
		if (res.code == 200){
			var url = 'prd_id[]=' + id;
			displayPopup(orderCreateSelectConditionnement, '', '/admin/ajax/products/popup-colisage-selector.php?' + url, '', 916);
			ncmd_refresh();
			return false;
		} else {
			ncmd_refresh();
			return false;
		}
	}, 'json');
}
/**	Cette fonction permet le rechargement de la page en ajoutant plusieurs produits au panier
*	data : prd_add[]=ID&prd_add[]=ID
*/
function ncmd_select_prds( data ){
	//rechargement de la page de création avec l'utilisateur choisi.
	$.get( ncmd_url+'?'+data, function( res ){
		if (res.code == '200'){
			var url = res.data;
			displayPopup(orderCreateSelectConditionnement, '', '/admin/ajax/products/popup-colisage-selector.php?' + url, '', 916);
			ncmd_refresh();
			return false;
		} else {
			ncmd_refresh();
			if ($('.success').length) {
				$(".success").html(res.message);
			} else {
				$('.error').remove();
				$('.success').remove();
				$("#popup-content").prepend('<div class="success">'+res.message+'</div>');
			}
			return false;
		}
	}, 'json');
}

/**	Cette fonction permet de recharger le panier
*/
function ncmd_refresh(){
	$.get( ncmd_url, function( html ){
		$('#ncmd_content').html( html );
		ncmd_init();
	});
}

/**
 *	Cette fonction permet de sélectionner le conditionnement.
 */
function parent_select_colisage( prd_ids, colisage_ids, colisage_qtes ){
	var ar_prd_ids = prd_ids.split(',');
	var ar_col_ids = colisage_ids.split(',');
	var ar_col_qtes = colisage_qtes.split(',');

	var data = '';
	var count = 0;
	var executed_prds = {};

	$.each(ar_prd_ids, function(index, value){
		if (count){
			data += "&";
		}
		if (executed_prds[value] != 1){
			data += "prd_add[]="+value;
			executed_prds[value] = 1;
		}
		data += "&prd_col_prop["+value+"]["+count+"][id]="+ar_col_ids[index]+"&prd_col_prop["+value+"]["+count+"][qte]="+ar_col_qtes[index];
		count++;
	});

    $.get( ncmd_url+'?'+data, function(){
		ncmd_refresh();
		return false;
	});
}