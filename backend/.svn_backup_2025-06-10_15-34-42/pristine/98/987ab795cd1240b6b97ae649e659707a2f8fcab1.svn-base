$(document).ready( function(){
	$('#riawebsitepicker .selectorview').click(function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
			$('#riadatepicker .selector').hide();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	});
	
	$('#riawebsitepicker .selector a').click( function(){
		window.location.href = 'index.php?wst='+$(this).attr('name');
	});

	if( typeof $('#sort-ver') != undefined && $('#sort-ver').length ){
		riaSortable.create({
			'table'	:	$('#cgv'),
			'url'	:	'/admin/ajax/cgv/ajax-position-update.php?ver=' + $('#sort-ver').val()
		});
	}
} );
function confirmCgvVersionsDelList(){
	return window.confirm(cgvConfirmSupressionListe);
	
}
function confirmCgvVersionsDel(){
	return window.confirm(cgvConfirmSupression);
}
function confirmCgvVersionsPublish(){
	return window.confirm(cgvAlertPublication);
}
function validCgvVersionAdd(frm){
	if( !trim(frm.name.value) ){
		alert(cgvAlertNomVide);
		frm.name.focus();
		return false;
	}
}
function validCgvVersionForm(frm){
	if( !trim(frm.name.value) ){
		alert(cgvAlertNomVide);
		frm.name.focus();
		return false;
	}
}
function confirmCgvArticleDel(){
	return window.confirm(cgvAlertSuppressionArticle);
}
function confirmCgvArticleDelList(){
	return window.confirm(cgvAlertListeSuppressionArticle);
}
function validCgvArticleForm(frm){
	if( !trim(frm.name.value) ){
		alert(cgvAlertTitre);
		frm.name.focus();
		return false;
	}
	if( !trim(frm.desc.value) ){
		alert(cgvAlertContenu);
		frm.desc.focus();
		return false;
	}
}