<?php 
    global $config;

    if( !isset($_GET['cols'], $_GET['heads'], $_GET['author'], $_GET['date1'], $_GET['date2']) ){
		print _('Il manque des paramètres');
		exit;
    }

    require_once('excel/PHPExcel.php');
    $alphabet = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');

    // Création de l'objet PHPExcel
    $objPHPExcel = new PHPExcel();

    // Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
        ->setLastModifiedBy("riaStudio")
        ->setTitle(_("Export des rapports d'appels"))
        ->setSubject(_("Export des rapports d'appels"))
        ->setDescription(_("Export des rapports d'appels"))
        ->setKeywords("export rapports appels")
        ->setCategory("");

    // Création du fichier
    $objWorksheet = $objPHPExcel->getActiveSheet();

    // 1° feuille : Contient l'entête des commande
    $objWorksheet->setTitle(_('Rapports d\'appels'));

    $dates = array('date1' => date('Y-m-d H:i:s', strtotime($_GET["date1"])) ,'date2' => date('Y-m-d H:i:s', strtotime($_GET["date2"])));

	$author = isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] > 0 ? $_GET['author'] : 0;

    $reports = gcl_calls_get_by_view("", 0, 0, $author, $dates);

    if (!is_array($reports)) {
		print _('Erreur dans la récupération des rapports d\'appels');
		exit;
    }

    $line = 1;
    $count_lv1 = $count_lv2 = 0;
    $column = '';

    // 1° feuille : Nom des colonnes
    foreach($_GET['cols'] as $nom_col){
        if ($count_lv1 > 25){
            $count_lv2++;
            $count_lv1 = 0;
        }
        
        if ($count_lv2 > 25){
            $count_lv2 = 0;
        }
        
        if (!$count_lv2){
            $column = $alphabet[$count_lv1];
            $count_lv1++;
        } else {
            $column = $alphabet[$count_lv2 - 1].$alphabet[$count_lv1];
            $count_lv1++;
        }

        $objWorksheet->setCellValue($column.$line, $nom_col);
        $objWorksheet->getColumnDimension($column)->setAutoSize(true);
    }



    $objWorksheet->getStyle($alphabet[0].'1:'.$column.$line)->getFont()->setBold(true);
	$objWorksheet->getStyle($alphabet[0].'1:'.$column.$line)->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet->getStyle($alphabet[0].'1:'.$column.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
	$objWorksheet->getStyle($alphabet[0].'1:'.$column.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
    $objWorksheet->getStyle($alphabet[0].'1:'.$column.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
    
    // Réinitialisation des compteurs pour les collones
    $count_lv1 = $count_lv2 = 0;

    // Enregistre les rapports d'appels
    $line = 2;
	foreach( $reports as $key => $report ){
        if ($key != 'total_rows'){
            $dest_id = $report['gcl_usr_dst'];
            if ($report['gcl_type'] != 2){
                $dest_id = $report['gcl_author_id'];
            }
            $r_dest = gu_users_get( $dest_id );
            if( $r_dest && ria_mysql_num_rows($r_dest) ){
                $dest = ria_mysql_fetch_assoc( $r_dest );
            }
    
            $author_id = $report['gcl_author_id'];
            if ($report['gcl_type'] != 2){
                $author_id = $report['gcl_usr_dst'];
            }
            $r_author = gu_users_get( $author_id );
            if( $r_author && ria_mysql_num_rows($r_author) ){
                $auth = ria_mysql_fetch_assoc( $r_author );
            } 
    
            foreach($_GET['heads'] as $nom_col){
                $cell_value = '';
                if ($count_lv1 > 25){
                    $count_lv2++;
                    $count_lv1 = 0;
                }
                
                if ($count_lv2 > 25){
                    $count_lv2 = 0;
                }
                
                if (!$count_lv2){
                    $column = $alphabet[$count_lv1];
                    $count_lv1++;
                } else {
                    $column = $alphabet[$count_lv2 - 1].$alphabet[$count_lv1];
                    $count_lv1++;
                }
    
                switch($nom_col){
                    case 'date' : {
                        $cell_value = date('d/m/Y à H:i', strtotime($report['gcl_date_created']));
                        break;
                    }
                    case 'id_author' : {
                        $cell_value = $report['gcl_author_id'];
                        break;
                    }
                    case 'name_author' : {
                        $cell_value = isset($auth) ? $auth['adr_lastname'] : '' ;
                        break;
                    }
                    case 'firstname_author' : {
                        $cell_value = isset($auth) ? $auth['adr_firstname'] : '';
                        break;
                    }
                    case 'society_author' : {
                        $cell_value = isset($auth) ? $auth['society'] : '';
                        break;
                    }
                    case 'id_dest' : {
                        $cell_value = $report['gcl_usr_dst'];
                        break;
                    }
                    case 'name_dest' : {
                        $cell_value = isset($dest) ? $dest['adr_lastname'] : '' ;
                        break;
                    }
                    case 'firstname_dest' : {
                        $cell_value = isset($dest) ? $dest['adr_firstname'] : '';
                        break;
                    }
                    case 'society_dest' : {
                        $cell_value = isset($dest) ? $dest['society'] : '';
                        break;
                    }
                    case 'number' : {
                        $cell_value = $report['gcl_phone'] ;
                        break;
                    }
                    case 'duration' : {
                        $duree = $report['gcl_duration'];
                        $heures = floor($duree/60/60);
                        $minutes = floor($duree/60) - $heures*60;
                        $secondes = floor($duree) - $minutes*60 - $heures*60*60;
                        $call_time = '';
                        if ($heures!=0){
                            $call_time .= $heures.' '.($heures > 1 ? _('heures') : _('heure')).' ';
                        }
                        if ($minutes!=0){
                            $call_time .= $minutes.' '.($minutes > 1 ? _('minutes') : _('minute')).' ';
                        }
                        $call_time .= $secondes.' '.($secondes > 1 ? _('secondes') : _('seconde'));
    
                        $cell_value = $call_time;
                        break;
                    }
                    case 'comments' : {
                        $cell_value = str_replace(array("\r", "\n"), array(" ", " "), $report['gcl_comment']);
                        break;
                    }
                }
        
                $objWorksheet->setCellValue($column.$line, $cell_value);
            }
    
            // Réinitialisation des compteurs pour les collones
            $count_lv1 = $count_lv2 = 0;
            $line++;
        }
	}


    $objPHPExcel->setActiveSheetIndex(0);

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
    $objWriter->save($config['doc_dir'].'/export-calls-report-'.$_SESSION['usr_id'].'.xls');
    exit;
