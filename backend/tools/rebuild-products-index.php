<?php

set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once( 'products.inc.php' );

	if( isset($argv[1]) && (!is_numeric($argv[1]) || $argv[1]<0) ){
		print "Veuillez renseigner un identifiant de produit valide (numéric supérieur à zéro).\n";
		return;
	}
	if( isset($argv[2]) && (!is_numeric($argv[2]) || $argv[2]<0) ){
		print "Veuillez renseigner un identifiant de catégorie valide (numéric supérieur à zéro).\n";
		return;
	}
	
	$prd = isset($argv[1]) && is_numeric($argv[1]) && $argv[1]>0 ? $argv[1] : 0;
	$cat = isset($argv[2]) && is_numeric($argv[2]) && $argv[2]>0 ? $argv[2] : 0;
	$recursive = isset($argv[3]) && $argv[3] ? true : false;

	prd_products_index_rebuild( $prd, false, $cat, $recursive );


