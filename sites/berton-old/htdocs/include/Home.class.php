<?php

namespace Berton\App;

use PageService;
use ProductsService;
use BrandsService;

final class BertonHome
{
	/**	Tableau des informations de base des sections
	 * @var	array
	 */
	const BHSECTIONS = [
		[
			'id'	=> 1,
			'title'	=> 'Nos produits',
			'root'	=> '__sectionOurProducts'
		],
		[
			'id'	=> 2,
			'title'	=> 'Nos univers métiers',
			'root'	=> '__sectionOurUniverses'
		],
		[
			'id'	=> 3,
			'title'	=> 'Nos marques',
			'root'	=> '__sectionOurbrands'
		]
	];

	/**	Instance PageService
	 * @var PageService|null
	 */
	private $Page = null;

	/**	Tableau contenant les différentes sections de la HP
	 * @var	array|null
	 */
	private $sections = null;

	/**	Charge l'instance de la classe
	 * @param	PageService		$Page	Obligatoire, une instance de PageService
	 * @return	void
	 */
	public function __construct(PageService $Page)
	{
		$this->Page = $Page;
	}

	/**	Retourne les informations d'une section
	 * @param	int		$id			Obligatoire, Identifiant de la section
	 * @param	bool	$onlydata	Optionnel, True pour récupérer seulement les données
	 * @return	mixed	Tableau des données ou booléen
	 */
	public function getSection($id, $onlydata = true)
	{
		$section = $this->__getSectionById($id);

		if (is_bool($onlydata) && $onlydata) {
			return is_array($section) && isset($section['data']) ? $section['data'] : false;
		}
		return $section;
	}

	/**	Retourne toutes les informations de toutes les sections
	 * @return	bool|array	Tableau contenant les informations de chaque section, false sinon
	 */
	public function getSections()
	{
		foreach (self::BHSECTIONS as $section) {
			$this->__getSectionById($section['id']);
		}
		return is_null($this->sections) ? false : $this->sections;
	}

	/**	Retourne les informations de base d'un section
	 * @param	int			$id	Obligatoire, Identifiant de la section
	 * @return	bool|array	Tableau des informations de base de la section, false sinon
	 */
	private function __getSectionDataById($id)
	{
		if (!is_numeric($id) || $id < 1) {
			return false;
		}

		foreach (self::BHSECTIONS as $section) {
			if ($section['id'] == $id) {
				return $section;
			}
		}
		return false;
	}

	/**	Charge et retourne les informations d'une section
	 * @param	int			$id	Obligatoire, Identifiant de la section
	 * @return	bool|array	Informations sur la section, false sinon
	 */
	private function __getSectionById($id)
	{
		if (!is_numeric($id) || $id < 1) {
			return false;
		}

		if (is_null($this->sections)) {
			$this->__loadSection($id);
		}

		if (is_null($this->sections)) {
			return false;
		}

		foreach ($this->sections as $section) {
			if ($section['id'] == $id) {
				return $section;
			}
		}
		$this->__loadSection($id);

		foreach ($this->sections as $section) {
			if ($section['id'] == $id) {
				return $section;
			}
		}

		return false;
	}

	/**	Charge les données d'une section
	 * @param	int		$id	Obligatoire, Identifiant de la section
	 * @return	bool	True en cas de succès, false sinon
	 */
	private function __loadSection($id)
	{
		$section = $this->__getSectionDataById($id);

		if (!$section) {
			return false;
		}

		if (!is_array($this->sections)) {
			$this->sections = [];
		}
		$res = call_user_func([$this, $section['root']]);
		$section['data'] = $res;
		$this->sections[] = $section;

		return true;
	}

	/**	Permet la récupéreration des produits mis en avant
	 * @return	array|bool	Tableau des informations sur les produits, false sinon
	 */
	private function __sectionOurProducts()
	{
		global $config;

		$data = $this->Page->getData();

		if (!isset($data['fields']) || !is_array($data['fields'])) {
			return false;
		}

		if (!isset($data['fields']['field' . $config['fld_cms_hp_our_products_prds']]) || !is_array($data['fields']['field' . $config['fld_cms_hp_our_products_prds']])) {
			return false;
		}
		$str_prds = $data['fields']['field' . $config['fld_cms_hp_our_products_prds']];

		if (!isset($str_prds['value']) || !is_string($str_prds['value'])) {
			return false;
		}
		$ex_prds = explode(',', $str_prds['value']);

		if (!is_array($ex_prds) || !count($ex_prds)) {
			return false;
		}
		require_once 'Services/Catalog/Products.class.php';

		$Products = new ProductsService($ex_prds, $config['cat_root']);
		$Products->setAttribute('published', true)
				->setAttribute('cat_children', true)
				->setAttribute('with_img', true);

		return $Products->getProducts();
	}

	/**	Permet la récupéreration des univers métiers
	 * @return	array|bool	Tableau des informations sur les univers métiers, false sinon
	 */
	private function __sectionOurUniverses()
	{
		global $config;

		$data = $this->Page->getData();

		if (!isset($data['children']) || !is_array($data['children']) || count($data['children']) <= 0) {
			return false;
		}
		require_once 'UniversesMenu.class.php';

		foreach ($data['children'] as $child) {
			if (!isset($child['fields']) || !is_array($child['fields'])) {
				continue;
			}
			$child_flds = $child['fields'];

			if (!isset($child_flds['field' . $config['fld_cms_content_type']]) || $child_flds['field' . $config['fld_cms_content_type']]['value'][0] != 'Nos univers métiers') {
				continue;
			}
			$UniversesMenu = BertonUniversesMenu::getInstance();

			return $UniversesMenu->getTreeMenu();
		}

		return false;
	}

	/**	Permet la récupéreration des marques mises en avant
	 * @return	array|bool	Tableau des informations sur les marques, false sinon
	 */
	private function __sectionOurbrands()
	{
		global $config;

		$data = $this->Page->getData();

		if (!isset($data['fields']) || !is_array($data['fields'])) {
			return false;
		}

		if (!isset($data['fields']['field' . $config['fld_cms_hp_our_brands_brds']]) || !is_array($data['fields']['field' . $config['fld_cms_hp_our_brands_brds']])) {
			return false;
		}
		$str_brds = $data['fields']['field' . $config['fld_cms_hp_our_brands_brds']];

		if (!isset($str_brds['value']) || !is_string($str_brds['value'])) {
			return false;
		}
		$ex_brds = explode(',', $str_brds['value']);

		if (!is_array($ex_brds) || !count($ex_brds)) {
			return false;
		}
		require_once 'Services/Catalog/Brands.class.php';

		$Brands = new BrandsService($ex_brds);
		$Brands->setAttribute('publish', true)
			->setAttribute('with_img', true)
			->setAttribute('cfg_img', 'brand');

		return $Brands->getBrands();
	}
}
