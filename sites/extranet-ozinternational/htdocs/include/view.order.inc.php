<?php
	require_once('orders.inc.php');
	require_once('i18n.inc.php');

	define('STEP_CART', 1);
	define('STEP_DELIVERY', 2);
	define('STEP_COMMENT', 3);
	define('STEP_REVIEW', 4);
	define('STEP_FINISH', 5);

	/**
	 * Charge le html pour le mini panier (dans l'entête du site).
	 * @return string Le html du panier
	 */
	function view_mini_cart(){
		global $config;

		$total = $nb_prds = 0;
		if( isset($_SESSION['ord_id']) && $_SESSION['ord_id'] ){
			$res = ord_products_get_count( $_SESSION['ord_id'], false, false, false, false, false, false );

			if( $res && ria_mysql_num_rows($res) ){
				$r = ria_mysql_fetch_assoc( $res );

				$nb_prds = $r['count_prd'];
				$total = $config['usr_show_price']=='ht' ? $r['total_ht'] : $r['total_ttc'];
			}
		}

		return '
			<div class="cart-infos">
				<div class="bar">'.i18n::get('Mon panier').'<span class="icon-arrow-down-mini"></span></div>
				<div>
					<div class="icon-shopping-round"></div>
					<div class="products">
						'.number_format( $nb_prds, 0 , ',', ' ' ).' '.i18n::get('produits').'<br />
						'.htmlspecialchars( view_price($total, 2, ',', ' ', false, $config['site_symbol_devis'], 'last') ).'<br />
						<a href="'.rew_rewritemap_translate_get( '/mon-panier/', i18n::getLang() ).'" title="'.i18n::get('Voir le panier').'">'.i18n::get('> Voir le panier').'</a>
					</div>
				</div>
			</div>
		';
	}

	/** Cette fonction affiche le fil d'ariane propre à la commande
	 *	@param int $step Obligatoire, étape en cours
	 *	@return string Le HTML du fil d'ariane
	 */
	function view_order_breadcrumb( $step ){
		if( !in_array($step, array(STEP_CART, STEP_DELIVERY, STEP_COMMENT, STEP_REVIEW, STEP_FINISH)) ){
			$step = STEP_CART;
		}

		$html = '';

		switch( $step ){
			case STEP_CART: {
				$html .= '
					<div class="h1">'.i18n::get( 'Mon panier' ).'
						<span class="steps">
							<span class="step">
								<span class="icon-step">1</span>
								<span>'.i18n::get('Mon panier').'</span>
							</span>
							<span class="step">
								<span class="icon-step-disabled">2</span>
								<span>'.i18n::get('Livraison').'</span>
							</span>
							<span class="step">
								<span class="icon-step-disabled">3</span>
								<span>'.i18n::get('Consignes de livraison').'</span>
							</span>
							<span class="step last">
								<span class="icon-step-disabled">4</span>
								<span>'.i18n::get('Confirmation').'</span>
							</span>
						</span>
					</div>
				';
				break;
			}
			case STEP_DELIVERY: {
				$html .= '
					<div class="h1">'.i18n::get( 'Livraison' ).'
						<span class="steps">
							<a href="'.rew_rewritemap_translate_get( '/mon-panier/', i18n::getLang() ).'" class="step selected">
								<span class="icon-step-disabled">1</span>
								<span>'.i18n::get('Mon panier').'</span>
							</a>
							<span class="step">
								<span class="icon-step">2</span>
								<span>'.i18n::get('Livraison').'</span>
							</span>
							<span class="step">
								<span class="icon-step-disabled">3</span>
								<span>'.i18n::get('Consignes de livraison').'</span>
							</span>
							<span class="step last">
								<span class="icon-step-disabled">4</span>
								<span>'.i18n::get('Confirmation').'</span>
							</span>
						</span>
					</div>
				';
				break;
			}
			case STEP_COMMENT: {
				$html .= '
					<div class="h1">'.i18n::get( 'Commentaires' ).'
						<span class="steps">
							<a href="'.rew_rewritemap_translate_get( '/mon-panier/', i18n::getLang() ).'" class="step selected">
								<span class="icon-step-disabled">1</span>
								<span>'.i18n::get('Mon panier').'</span>
							</a>
							<a href="'.rew_rewritemap_translate_get( '/commander/livraison/', i18n::getLang() ).'" class="step selected">
								<span class="icon-step-disabled">2</span>
								<span>'.i18n::get('Livraison').'</span>
							</a>
							<span class="step selected">
								<span class="icon-step">3</span>
								<span>'.i18n::get('Commentaires').'</span>
							</span>
							<span class="step last">
								<span class="icon-step-disabled">4</span>
								<span>'.i18n::get('Confirmation').'</span>
							</span>
						</span>
					</div>
				';
				break;
			}
			case STEP_REVIEW: {
				$html .= '
					<div class="h1">'.i18n::get( 'Confirmation' ).'
						<span class="steps">
							<a href="'.rew_rewritemap_translate_get( '/mon-panier/', i18n::getLang() ).'" class="step selected">
								<span class="icon-step-disabled">1</span>
								<span>'.i18n::get('Mon panier').'</span>
							</a>
							<a href="'.rew_rewritemap_translate_get( '/commander/livraison/', i18n::getLang() ).'" class="step selected">
								<span class="icon-step-disabled">2</span>
								<span>'.i18n::get('Livraison').'</span>
							</a>
							<a href="'.rew_rewritemap_translate_get( '/commander/consignes-livraison/', i18n::getLang() ).'" class="step selected">
								<span class="icon-step-disabled">3</span>
								<span>'.i18n::get('Livraison').'</span>
							</a>
							<span class="step selected last">
								<span class="icon-step">4</span>
								<span>'.i18n::get('Confirmation').'</span>
							</span>
						</span>
					</div>
				';
				break;
			}
			case STEP_FINISH: {
				$html .= '
					<div class="h1">'.i18n::get( 'Commande finalisée' ).'</div>
				';
				break;
			}
		}

		return $html;
	}

	/** Cette fonction affiche un sous menu pour quelque page du site
	 *	@param int $step Obligatoire, étape en cours
	 *	@return string Le HTML du menu
	 */
	function view_order_menu( $step ){
		if( !in_array($step, array('cart', 'batch', 'bestseller', 'histo', 'reliquats', 'download')) ){
			$step = 'cart';
		}

		return '
			<ul class="menu">
				<li><a '.( $step=='cart' ? 'class="active"' : '' ).' href="'.rew_rewritemap_translate_get( '/mon-panier/', i18n::getLang() ).'" title="'.i18n::get('Panier').'">'.i18n::get('Panier').'</a></li>
				<li><a '.( $step=='batch' ? 'class="active"' : '' ).' href="'.rew_rewritemap_translate_get( '/commande-rapide/', i18n::getLang() ).'" title="'.i18n::get('Commande rapide').'">'.i18n::get('Commande rapide').'</a></li>
				<li><a '.( $step=='bestseller' ? 'class="active"' : '' ).' href="'.rew_rewritemap_translate_get( '/produits-les-plus-commandes/', i18n::getLang() ).'" title="'.i18n::get('Produits les plus commandés').'">'.i18n::get('Produits les plus commandés').'</a></li>
				<!-- li><a '.( $step=='histo' ? 'class="active"' : '' ).' href="'.rew_rewritemap_translate_get( '/mon-compte/commandes/', i18n::getLang() ).'" title="'.i18n::get('Historiques').'">'.i18n::get('Historiques').'</a></li>
				<li><a '.( $step=='reliquats' ? 'class="active"' : '' ).' href="'.rew_rewritemap_translate_get( '/mon-compte/reliquats/', i18n::getLang() ).'" title="'.i18n::get('Reliquats').'">'.i18n::get('Reliquats').'</a></li>
				<li><a class="'.( $step=='download' ? 'active ' : '' ).'last" href="'.rew_rewritemap_translate_get( '/telechargements/', i18n::getLang() ).'" title="'.i18n::get('Téléchargements').'">'.i18n::get('Téléchargements').'</a></li -->
			</ul>
		';
	}

	/**	Cette fonction affiche le contenu d'une commande / panier
	 * @param int $ord_id obligatoire, identifiant de la commande
	 * @param bool $readonly Optionnel, par défaut la commande est en mode ecriture (quantité modifiable, suppression de ligne possible), mettre True pour qu'elle soit en lecture seule
	 * @return string Le HTML du contenu d'une commande
	 */
	function view_order( $ord_id, $readonly=false, $save_order=false, $reorder=false, $model=false, $no_link=false ){
		// if( !ord_orders_exists($ord_id) ){
		// 	return false;
		// }

		global $config;

		$html = '';

		$ar_product = array();
		$ar_reorder = array();
		$prd_offert = array();

		$state_id = false;

		$total_ht = $total_ht_available = $total_ht_unavailable = $total_ttc = $port_ht = $port_ttc = 0;
		$ar_tva_rate = array();

		if( ord_orders_exists($ord_id) ){
			$order = ria_mysql_fetch_assoc( ord_orders_get( 0, $ord_id ) );
			$state_id = $order['state_id'];
			// $state_id = ord_orders_get_state( $ord_id );
			$ar_stock = $ar_no_stock = array();

			$r_oprd = ord_products_get( $ord_id );
			if( $r_oprd ){
				while( $oprd = ria_mysql_fetch_assoc($r_oprd) ){
					$rp = prd_products_get_simple( $oprd['id'], '', false, 0, false, false, true );
					if( !$rp || !ria_mysql_num_rows($rp) ){
						continue;
					}

					$p = ria_mysql_fetch_assoc( $rp );

					if( $model && !$p['publish'] ){
						continue;
					}

					// Cette condition fait suite à un problème lors de promotion sur riashop qui offre des produits pour l'achat de produits désignés
					// Si le prix n'est pas à 0 continuer le workflow.
					// ajout de la condition price_ttc pour résoudre une problématique lorsque les frais de port était à 0
					if($oprd['price_ht'] != 0 OR $oprd['price_ttc'] != 0)
					{
						$oprd['price_ht'] = $p['price_ht'];
						$oprd['price_ttc'] = $p['price_ttc'];
					}

					$oprd['tva_rate'] = $p['tva_rate'];
					$oprd['total_ht'] = $p['price_ht'] * $oprd['qte'];
					$oprd['total_ttc'] = $p['price_ttc'] * $oprd['qte'];

					if( $model ){
						$oprd['qte'] = 0;
					}

					if( prd_products_is_port($oprd['ref']) ){
						$port_ht += $oprd['price_ht'];
						$port_ttc += $oprd['price_ttc'];
						continue;
					}

					$oprd['barcode'] 		= $p['barcode'];
					$oprd['img_id'] 		= $p['img_id'];
					$oprd['stock'] 			= $p['stock'];
					$oprd['stock_livr'] 	= $p['stock_livr'];
					$oprd['follow_stock'] 	= $p['follow_stock'];
					$oprd['url_alias'] 		= prd_products_get_url( $p['id'], true, $config['cat_root'] );

					// Tarif d'origine sur le produit
					$tmp_sell_unit 		= fld_object_values_get( $p['id'], $config['fld_sell_unit'] );
					$sell_unit 			= is_numeric($tmp_sell_unit) && $tmp_sell_unit>1 ? $tmp_sell_unit : 1;
					$oprd['sell_unit'] 	= is_numeric($tmp_sell_unit) && $tmp_sell_unit>1 ? $tmp_sell_unit : 1;

					$price = prd_products_get_price_cached( $p['id'], 0, 0, 0, $sell_unit );
					if( isset($price['price_ht']) ){
						$oprd['price_origin'] = $price['price_ht'];
					}

					if( $p['price_ht']==0 ){
						$prd_offert[] = $oprd;
					}elseif( $p['stock']>0 ){
						$ar_stock[] = $oprd;
					}else{
						$ar_no_stock[] = $oprd;
					}
				}
			}

			$ar_product = array_merge( $ar_stock, $ar_no_stock, $prd_offert );
		}

		$html .= '
			<table id="table-cart">
				'.( $model ? '<col class="auto" />' : '' ).'
				<col class="col-check" /><col class="col-td-name" /><col class="auto" /><col class="auto" /><col class="auto" /><col class="auto" /><col class="auto" /><col class="auto" />'.( !$readonly ? '<col class="auto" />' : '' ).'
				<thead>
					<tr>
						'.( $model ? '<th><input type="checkbox" name="checkall" id="checkall" value="" />' : '' ).'
						<th class="produit">'.i18n::get('Produit').'</th>
						<th>'.i18n::get('Désignation').'</th>
						<th>'.i18n::get('').'</th>
						<th>'.i18n::get('Prix tarif').'</th>
						<th>'.i18n::get('Remise').'</th>
						<th>'.i18n::get('Prix net').'</th>
						<th class="quantity">'.i18n::get('Quantité').'</th>
						<th>'.i18n::get('Total').'</th>
						'.( !$readonly && !$model ? '<th>'.( sizeof($ar_product) ? '<a title="Supprimer tous les articles actuellement dans votre panier." class="icon-delete-all" href="'.rew_rewritemap_translate_get( '/mon-panier/', i18n::getLang() ).'?del-all=1"></a>' : '' ).'</th>' : '' ).'
					</tr>
				</thead>
				<tbody>
		';

		if( sizeof($ar_product) ){
			// récupère UNE SEULE FOIS la liste des promotions automatiques

			$get_active = pmt_codes_get_active_for_order($order['id']);
			$pmt_codes_get = array();
			if( $rcod = pmt_codes_get( $get_active, null, true, false, true ) ){
				while( $c = ria_mysql_fetch_array($rcod) ){
                    ria_dump($c);
					if( pmt_codes_is_applicable('', 0, 0, false, false, $c, $order) === true ){
						$pmt_codes_get[] = $c;
					}
				}
			}

			foreach( $ar_product as $one_product ){
				if( $reorder && $state_id!=_STATE_BASKET_SAVE ){
					if( prd_products_is_available($one_product['id']) ){
						$ar_reorder[] = $one_product;
					}
				}

				$remise = 0;
				// if( isset($one_product['price_origin']) && is_numeric($one_product['price_origin']) && $one_product['price_origin']>0 && $one_product['price_ht']!=$one_product['price_origin']){
				// 	$remise = round( (1 - $one_product['price_ht'] / $one_product['price_origin']) * 100 );
				// }
				$iprc = get_products_prices( $one_product );

				if ( $iprc['is_price'] !== false ) {
					if( $one_product['qte'] >= $iprc['all_prices'][0]['qte_min'] ){
						$remise = $iprc['all_prices'][0]['pourcent'];
					}
				}

				$remise = max($iprc['is_promo'], $iprc['is_remise'], $remise);

				// Si le produit courant est un produit offert alors on lui assigne la valeur 0 sinon appliquer la remise nécessaire.
				if( $one_product['type_promo']==_PMT_TYPE_PRD ){
					$remise = false;
					$price_net = 0.0;
				}
				else {
					$price_net = $one_product['price_origin'] - ( $one_product['price_origin'] * $remise / 100);
				}

				$one_product['price_ht'] = $price_net;
				$one_product['price_ttc'] = $price_net * $one_product['tva_rate'];


				$total_ht += $one_product['price_ht'] * $one_product['qte'];
				$total_ttc += $one_product['price_ttc'] * $one_product['qte'];

				if( !isset($ar_tva_rate[ $one_product['tva_rate'] ]) ){
					$ar_tva_rate[ $one_product['tva_rate'] ] = 0;
				}

				$ar_tva_rate[ $one_product['tva_rate'] ] += ( $one_product['price_ttc'] - $one_product['price_ht'] ) * $one_product['qte'];

				$txt_promo = '';
				if( sizeof($pmt_codes_get) ){
					$txt_promo = ord_products_show_promotion( $one_product['id'], $one_product['price_ttc'], $one_product['qte'], false, null, $pmt_codes_get );
				}
				
				$html .= '
					<tr>
						'.( $model ? '<td><input type="checkbox" name="add-model[]" value="'.$one_product['id'].'" /></td>' : '' ).'
						<td class="product-image">
							'.view_img( $one_product['img_id'], $config['img_sizes']['product-second'] ).'
							'.view_favorite_icon( $one_product['id'], true ).'
						</td>
						<td class="product">
				';

				if( $no_link ){
					$html .= '
							<span>'.htmlspecialchars( $one_product['title'] ).'</span>
					';
				}else{
					$html .= '
							<a href="'.rew_strip( $one_product['url_alias'] ).'">'.htmlspecialchars( $one_product['title'] ).'</a>
					';
				}

				$stock_info = view_stock_info($one_product, $one_product['qte']);

				if( $one_product['qte'] > $one_product['stock'] ){

					$total_ht_available += ($one_product['price_ht'] * $one_product['stock']);
					$total_ht_unavailable += ( $one_product['price_ht'] * ($one_product['qte'] - $one_product['stock']) );
				}else{

					$total_ht_available += ($one_product['price_ht'] * $one_product['qte']);
				}

				$html .= '
							<span class="ref">Réf. '.htmlspecialchars( $one_product['ref'] ).'</span>
							'.( trim($txt_promo)!='' ? $txt_promo : $stock_info ).'
						</td>
						<td class="barcode">
				';

				// $one_product['barcode'] = ''; // a commenter avant de proder, erreur php en dev

				if( trim($one_product['barcode'])!='' ){
					$html .= '
							'.view_barcode($one_product['barcode']).'
							'.htmlspecialchars( $one_product['barcode'] ).'
					';
				}

				$html .= '
						</td>
						<td class="price">
							'.view_price( $one_product['price_origin'], 2, ',', ' ', false, false, 'last', 'ht' ).'
						</td>
						<td class="price">
							'.( $remise>0 ? number_format($remise, 0, ',', ' ').'%' : '' ).'
						</td>
						<td class="price">
							'.view_price( $one_product['price_ht'], 2, ',', ' ', false, false, 'last', 'ht' ).'
						</td>
				';

				if( $one_product['cod']>0 && $one_product['price_ht']==0 ){
					$html .= '
						<td class="quantity readonly">
							<span class="msg-pmt-reduc">'.$one_product['qte'].'</span>
						</td>
					';
				}elseif( !$readonly ){
					$min = $model ? 0 : $one_product['sell_unit'];

					$html .= '
						<td class="quantity">
							<span class="info">UV : '.$one_product['sell_unit'].'</span>
							<a class="icon-minus"></a>
							<input type="text" name="qty['.$one_product['id'].']" value="'.$one_product['qte'].'" data-min="'.$min.'" data-step="'.$one_product['sell_unit'].'" />
							<a class="icon-plus"></a>
						</td>
					';
				}else{
					$html .= '
						<td class="quantity readonly">
							'.$one_product['qte'].'
						</td>
					';
				}

				$html .= '
						<td class="price">
							'.view_price( $one_product['price_ht'] * $one_product['qte'], 2, ',', ' ', false, false, 'last', 'ht' ).'
						</td>
				';

				if( !$readonly && !$model ){
					if( !($one_product['cod']>0 && $one_product['price_ht']==0) ){
						$html .= '
							<td class="delete">
								<a data-prd-id="'.$one_product['id'].'" href="#" class="icon-delete" title="'.i18n::get('Supprimer').'"></a>
							</td>
						';
					}
				}

				$html .= '
					</tr>
				';

				if( !$model ){
					$html .= '
						<tr class="line-comment">
							<td colspan="9" id="comment-'.$one_product['id'].'-'.$one_product['line'].'">
								'.i18n::get('Commentaire').' : '.( trim($one_product['notes']) ? htmlspecialchars( $one_product['notes'] ).( !$readonly ? ' - ' : '' ) : '' ).'
								'.( !$readonly ? '<a href="#" class="edit-comment" data-prd-id="'.$one_product['id'].'" data-line-id="'.$one_product['line'].'" data-comment="'.htmlspecialchars( $one_product['notes'] ).'">'.i18n::get('Modifier').'</a>' : '' ).'
							</td>
						</tr>
					';
				}
			}
		}else{
			$html .= '
				<tr>
					<td colspan="9" class="product-image">
						'.i18n::get('Aucun article n\'est présent dans votre panier').'
					</td>
				</tr>
			';
		}
		$html .= '
				</tbody>
			</table>

			<div class="summary">
				<div class="prices">
					<div class="label">'.i18n::get('Total HT produits disponibles').'</div>
					<div class="price">'.view_price( $total_ht_available, 2, ',', ' ', null, false, 'last', 'ht' ).'</div>
					<div class="label">'.i18n::get('Total HT produits indisponibles').'</div>
					<div class="price">'.view_price( $total_ht_unavailable, 2, ',', ' ', null, false, 'last', 'ht' ).'</div>
					<div class="label">'.i18n::get('Total produit HT').'</div>
					<div class="price">'.view_price( $total_ht, 2, ',', ' ', null, false, 'last', 'ht' ).'</div>
		';

		if( !$model ){
			foreach( $ar_tva_rate as $tva=>$total ){
				$tva = ($tva-1) * 100;
				$html .= '
						<div class="label">'.str_replace('#param[tva]#', $tva, i18n::get('TVA #param[tva]#%')).'</div>
						<div class="price">'.view_price( $total, 2, ',', ' ', null, false, 'last', 'ttc' ).'</div>
				';
			}
		}else{
			$html .= '
					<div class="label">'.i18n::get('TVA').'</div>
					<div class="price">'.view_price( $total_ttc - $total_ht, 2, ',', ' ', null, false, 'last', 'ttc' ).'</div>
			';
		}

		if( !$model ){
			$delivery_outside_france = false;
			if (!empty($order['adr_delivery'])) {
				$radresses = gu_adresses_get($config['usr_id'], $order['adr_delivery']);
				if ($radresses && ria_mysql_num_rows($radresses)) {
					$adresse = ria_mysql_fetch_assoc($radresses);
					if (!empty($adresse['country']) && strtolower($adresse['country']) != 'france' && $adresse['country'] != '') {
						$delivery_outside_france = true;
					}
				}
			}

			if ( $total_ht_available >= $config['franco'] ){
				$port_ht = 0;
			}

			$html .= '
					<div class="label">'.i18n::get('Frais de port* si livraison de suite').( $port_ht > 0 ? '<br /><span class="no-franco">'.i18n::get('Le franco n’est pas atteint').'</span>' : '').($port_ht == 0 && $delivery_outside_france ? '<br /><span class="no-franco">'.i18n::get('Nous vous informons que les frais de port seront calculés par notre équipe d\'administration des ventes').'</span>' : '').'</div>';

			$html .= '<div class="price">'.($port_ht == 0 && $delivery_outside_france ? '-- €' : view_price( $port_ht, 2, ',', ' ', null, false, 'last', 'ht' )).'</div>
			';
		}

		if( isset($order['pmt_id']) && $order['pmt_id'] ){
			$r_promo = pmt_codes_get( $order['pmt_id'] );
			if( $r_promo && ria_mysql_num_rows($r_promo) ){
				$promo = ria_mysql_fetch_assoc( $r_promo );

				$remise = number_format( ($total_ttc + $port_ttc) - $order['total_ttc'], 2 , ',', ' ' );
				$total_ttc = $order['total_ttc'] - $port_ttc;

				$html .= '
					<div class="label remise">'.i18n::get('Remise').'</div>
					<div class="price remise">
						- '.str_replace( ',00', '', number_format( $promo['discount'], 2 , ',', ' ' ) ).
						( $promo['discount_type']==1 ? '% soit '.$remise : '€' ).'
					</div>
				';


			}
		}

		$html .= '
					<!-- div class="label">'.i18n::get('HTDEE').'</div>
					<div class="price">9€19</div -->
					<div class="label">'.i18n::get('Total TTC').'</div>
					<div class="price bold">'.view_price( $total_ttc + $port_ttc, 2, ',', ' ', null, false, 'last', 'ttc' ).'</div>
				</div>
		';

		if( !$readonly && !$model ){
			$exclude = array( 6, 7 );

			$date_min = time();
			$i=0;
			while( $i<4 ){
				$date_min += 86400;
				$days = date( 'N', $date_min );

				if( !in_array($days, $exclude) ){
					$i++;
				}
			}

			$date_max = strtotime( '+ 1 days', $date_min);
			$i=0;
			while( $i<4 ){
				$date_max += 86400;
				$days = date( 'N', $date_max );

				if( !in_array($days, $exclude) ){
					$i++;
				}

			}

			$html .= '
				<div class="shipping">
					'.i18n::get('Livraison estimée').':
					<div class="date">'.str_replace(
						array('#PARAM[date-start]#', '#PARAM[date-end]#'),
						array(date('d/m/Y', $date_min), date('d/m/Y', $date_max)),
						i18n::get('entre le #PARAM[date-start]# et le #PARAM[date-end]#')
					).'</div>
				</div>
			';
		}
		$html .= '
			<div class="shipping">
				* FRAIS DE PORT : les frais de port s’appliquent sur le total HT des produits disponibles
			</div>
		';

		if( $save_order ){
			if( !gu_users_get_is_locked($config['usr_id']) ){
				$html .= '
					<a href="'.rew_rewritemap_translate_get( '/commander/terminer/', i18n::getLang() ).'" title="'.i18n::get('Valider la commande').'" class="button save-order">
						<span class="icon-shopping-white-round"></span>
						'.i18n::get('Terminer la commande').'
					</a>
				';
			}else{
				$html .= '<br />'.str_replace(
					array('#param[debut lien contact]#', '#param[fin lien contact]#'),
					array('<a href="'.rew_rewritemap_translate_get('/nous-contacter/', i18n::getLang()).'">', '</a>'),
					i18n::get('Merci de prendre #param[debut lien contact]#contact avec nous#param[fin lien contact]# pour finaliser votre commande.')
				);
			}
		}elseif( !$readonly && !$model ){
			$html .= '
				<a href="'.rew_rewritemap_translate_get( '/commander/livraison/', i18n::getLang() ).'" title="'.i18n::get('Valider la commande').'" class="button">
					<span class="icon-shopping-white-round"></span>
					'.i18n::get('Commander').'
				</a>
			';
		}

		$html .= '
			</div>
		';

		if( !$model && $readonly ){
			$html .= '
				<div class="readonly-comments">
					<span class="title">'.i18n::get('Commentaires').' : </span>
					<span class="comments">'.htmlspecialchars( $order['comments'] ).'</span>
				</div>
			';
		}

		if( $reorder ){
			$html .= '
				<div class="print-cart">
					<a title="'.i18n::get('Imprimer cette page').'" href="javascript:print()">
						<span class="icon-print"></span>
						'.i18n::get('Imprimer le panier').'
					</a>
				</div>
			';
			if( $state_id!=_STATE_BASKET_SAVE ){
				if( sizeof($ar_reorder) ){
					$html .= '
						<form class="form reorder" action="'.rew_rewritemap_translate_get( '/mon-panier/', i18n::getLang() ).'" method="post">
					';

					foreach( $ar_reorder as $one_product ){
						$html .= '
							<input type="hidden" name="qty['.$one_product['id'].']" value="'.$one_product['qte'].'" />
						';
					}
					$html .= '
							<div class="left"></div>
							<div class="right">
								<input type="submit" name="add-to-cart" value="'.i18n::get( $model ? 'Ajouter à mon panier' : 'Repasser commande' ).'" />
								<a style="display: block; margin-top: -42px; margin-right: 268px;" class="button-medium invoice-download" href="'.rew_rewritemap_translate_get( '/telecharger-excel/', i18n::getLang() ).'?order='.$ord_id.'">
									Télécharger (Excel)
								</a>
							</div>
						</form>
					';
				}
			}else{
				if( isset($_SESSION['ord_id']) && $_SESSION['ord_id']==$ord_id ){
					$html .= '<div class="is-basket">Ceci est votre panier en cours</div>';
				}else{
					$html .= '
						<form class="form reorder" action="'.rew_rewritemap_translate_get( '/mon-panier/', i18n::getLang() ).'" method="post">
							<input type="hidden" name="basket" value="'.$ord_id.'" />
							<div class="left"></div>
							<div class="right">
								<input type="submit" name="restore" value="'.i18n::get('Reprendre').'" />
							</div>
						</form>
					';
				}
			}
		}
		return $html;
	}

	/** Cette fonction recalcul les frais de port d'une commande
	 *	@return bool
	 */
	function calculated_port_in_order(){
		global $config;

		if( !isset($_SESSION['ord_id']) || !ord_orders_exists($_SESSION['ord_id'], $config['usr_id'], $config['ar_state_ord_exists']) ){
			return true;
		}
		$rcorder = ord_orders_get_with_adresses($config['usr_id'], $_SESSION['ord_id']);
		$delivery_outside_france = false;

		if ($rcorder && ria_mysql_num_rows($rcorder)) {
			$order = ria_mysql_fetch_assoc($rcorder);
			if (!empty($order['dlv_country']) && strtolower($order['dlv_country']) != 'france' && $order['dlv_country'] != '') {
				$delivery_outside_france = true;
			}
		}

		$without_port = $amount_in_stock = 0;
		$r_oprd = ord_products_get( $_SESSION['ord_id'] );

		if( $r_oprd && ria_mysql_num_rows($r_oprd) ){
			while( $oprd = ria_mysql_fetch_assoc($r_oprd) ){

				if( prd_products_is_port($oprd['ref']) ){
					continue;
				}
				$rp = prd_products_get_simple( $oprd['id'], '', false, $config['cat_root'], true, false, false, false, array('have_stock'=>true) );

				if(!ria_mysql_num_rows($rp)){
					continue;
				}
				$prd = ria_mysql_fetch_assoc($rp);

				if( !is_numeric($prd['stock']) || $prd['stock'] <= 0 || $oprd['qte'] <= 0 ){
					continue;
				}

				if( $prd['stock'] >= $oprd['qte'] ){
					$amount_in_stock += $oprd['price_ht'] * $oprd['qte'];
					$without_port++;
					continue;
				}

				if( $prd['stock'] < $oprd['qte'] ){
					$amount_in_stock += $oprd['price_ht'] * $prd['stock'];
					$without_port++;
				}
			}
		}
		$port_amount = $config['prd_port']['amount'];


		if( $amount_in_stock >= $config['franco'] ){
			$port_amount = 0;
		}

		// Si livraison en dehors de france
		if ($delivery_outside_france) {
			$port_amount = 0;
		}

		if( !ord_orders_port_del($_SESSION['ord_id']) ){
			return false;
		}

		if( !$without_port ){
			return true;
		}

		$tva_rate = _TVA_RATE_DEFAULT;
		if( gu_users_is_tva_exempt( $config['usr_id'] ) ){
			$tva_rate = 1;
		}

		return ord_products_add_free( $_SESSION['ord_id'], $config['prd_port']['ref'], $config['prd_port']['name'], $port_amount, 1, null, '', $tva_rate );
	}
